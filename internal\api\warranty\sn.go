package warranty

import "time"

type WarrantiesResp struct {
	Detail       WarrantyDetail        `json:"warranty_detail"`
	ReturnInfo   *WarrantyReturnInfo   `json:"warranty_return_info,omitempty"`
	ExchangeInfo *WarrantyExchangeInfo `json:"warranty_exchange_info,omitempty"`
	RepairInfo   *WarrantyRepair       `json:"warranty_repair_info,omitempty"`
}

type WarrantyDetail struct {
	// warranty
	ID            int        `json:"id"`
	Status        int8       `json:"status"`
	Salesman      string     `json:"salesman"`
	CustomerName  string     `json:"customer_name"`
	CustomerPhone string     `json:"customer_phone"`
	Model         string     `json:"model"`
	BuyDate       *time.Time `json:"buy_date"`
	CreatedAt     *time.Time `json:"created_at"`
	ActivatedAt   string     `json:"activated_at"`
	ProductDate   *time.Time `json:"product_date"`
	Barcode       string     `json:"barcode"`
	Number        string     `json:"number"`
	// endpoint
	Name    string `json:"name"`
	Address string `json:"address"`
	Manager string `json:"manager"`
	Phone   string `json:"phone"`
	// agency
	//TopSecondAgency string `json:"top_second_agency"`
	TopAgency    string `json:"top_agency"`
	SecondAgency string `json:"second_agency"`
}

type WarrantyReturnInfo struct {
	Reason    string     `json:"reason"`
	CreatedAt *time.Time `json:"created_at"`
	// endpoint
	Name    string `json:"name"`
	Address string `json:"address"`
	Manager string `json:"manager"`
	Phone   string `json:"phone"`
}

type WarrantyExchangeInfo struct {
	BarcodeNew string     `json:"barcode_new"`
	Reason     string     `json:"reason"`
	CreatedAt  *time.Time `json:"created_at"`
	// endpoint
	Name    string `json:"name"`
	Address string `json:"address"`
	Manager string `json:"manager"`
	Phone   string `json:"phone"`
}

type WarrantyRepair struct{}
