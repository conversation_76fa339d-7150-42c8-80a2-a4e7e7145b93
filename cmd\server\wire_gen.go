// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/google/wire"
	"marketing/internal/config"
	"marketing/internal/dao/admin_user"
	"marketing/internal/dao/app_system"
	"marketing/internal/dao/endpoint"
	"marketing/internal/dao/prototype"
	"marketing/internal/dao/warranty"
	endpoint3 "marketing/internal/handler/admin/endpoint"
	"marketing/internal/handler/admin/mine"
	mine2 "marketing/internal/handler/agency/mine"
	"marketing/internal/handler/auth"
	base2 "marketing/internal/handler/base"
	"marketing/internal/provider"
	"marketing/internal/router"
	"marketing/internal/router/admin"
	"marketing/internal/router/agency"
	"marketing/internal/router/app"
	auth2 "marketing/internal/service/auth"
	"marketing/internal/service/base"
	endpoint2 "marketing/internal/service/endpoint"
	"marketing/internal/service/mkb"
	"marketing/internal/service/report"
	"marketing/internal/service/system"
)

// Injectors from wire.go:

func InitializeServer(cfg *config.Config) (*router.Router, error) {
	client := provider.ProvideDefaultRedis(cfg)
	infrastructureProvider := provider.NewInfrastructureProvider(cfg, client)
	userDao := provider.NewUserDao(cfg, infrastructureProvider)
	daoProvider := provider.NewDaoProvider(cfg, infrastructureProvider, userDao)
	db := provider.ProvideDefaultDB(cfg)
	appSystem := app_system.NewAppSystem(db)
	appSystemCacheInterface := app_system.NewAppSystemCache(client)
	appSystemSvc := system.NewAppSystemSvc(appSystem, appSystemCacheInterface)
	adminMenuInterface := system.NewAdminMenuSvc(db)
	serviceInterface := provider.NewAuthService(cfg, infrastructureProvider, daoProvider, appSystemSvc, appSystemCacheInterface, adminMenuInterface)
	authAuth := auth.NewAuth(serviceInterface)
	captchaSvc := base.NewCaptchaService(db)
	captchaHandler := base2.NewCaptchaHandler(captchaSvc)
	endpointDao := endpoint.NewEndpointDao(db)
	endpointEndpoint := endpoint2.NewEndpoint(db, endpointDao, userDao)
	endpoint4 := endpoint3.NewEndpoint(endpointEndpoint)
	endpointImageDao := endpoint.NewEndpointImageDao(db)
	imageService := endpoint2.NewEndpointImageService(db, endpointDao, endpointImageDao, userDao)
	imageInterface := endpoint3.NewEndpointImage(imageService)
	infoApplyDao := endpoint.NewEndpointInfoApplyDao(db)
	infoApplyService := endpoint2.NewEndpointInfoApplyService(db, endpointDao, infoApplyDao, userDao)
	infoApplyInterface := endpoint3.NewEndpointInfoApply(infoApplyService)
	wecomSyncService := system.NewSyncService(db, userDao, cfg)
	adminUserLog := admin_user.NewAdminUserLog(db)
	adminUserInterface := provider.NewAdminUserService(cfg, infrastructureProvider, wecomSyncService, appSystem, adminUserLog, daoProvider)
	serviceProvider := provider.NewServiceProvider(cfg, infrastructureProvider, daoProvider, adminUserInterface, wecomSyncService, appSystemSvc, adminUserLog, adminMenuInterface)
	settingDao := endpoint.NewSettingDao(db)
	settingService := endpoint2.NewSettingService(settingDao)
	settingHandler := endpoint3.NewSettingHandler(settingService)
	endpointRouter := admin.NewEndpointRouter(endpointEndpoint, endpoint4, imageInterface, infoApplyInterface, infrastructureProvider, serviceProvider, settingHandler)
	systemRouter := admin.NewSystemRouter(serviceProvider, infrastructureProvider)
	agencyRouter := admin.NewAgencyRouter(serviceProvider, infrastructureProvider, endpointEndpoint)
	afterSalesRouter := admin.NewAfterSalesRouter(userDao)
	regionService := base.NewRegionService(db)
	regionHandler := base2.NewRegionHandler(regionService)
	baseRouter := admin.NewBaseRouter(regionHandler, endpoint4, daoProvider, serviceProvider, cfg)
	mineInterface := mine.NewMine(adminUserInterface)
	mineRouter := admin.NewMineRouter(db, mineInterface)
	mineMineInterface := mine2.NewMine(adminUserInterface)
	agencyMineRouter := agency.NewMineRouter(mineMineInterface)
	interfaceWarranty := warranty.NewWarrantyDao(db)
	prototypeInterface := prototype.NewPrototypeDao(db)
	acDevicesUniqDao := provider.NewAcDevicesUniqDao(cfg, infrastructureProvider)
	prototypeCache := prototype.NewPrototypeCache()
	mkbService := mkb.NewMkbService(interfaceWarranty, endpointDao, prototypeInterface, userDao, acDevicesUniqDao, prototypeCache)
	mkbRouter := app.NewMkbRouter(mkbService)
	learningRoomService := report.NewLearningRoomService(interfaceWarranty)
	reportRouter := admin.NewReportRouter(learningRoomService)
	agencyBaseRouter := agency.NewBaseRouter(cfg, serviceProvider, daoProvider, regionHandler)
	reimbursementRouter := agency.NewReimbursementRouter()
	routerRouter := provideRouter(authAuth, captchaHandler, endpointRouter, systemRouter, agencyRouter, afterSalesRouter, baseRouter, mineRouter, agencyMineRouter, mkbRouter, cfg, serviceInterface, adminUserInterface, reportRouter, appSystemSvc, agencyBaseRouter, reimbursementRouter)
	return routerRouter, nil
}

// wire.go:

var RouterSet = wire.NewSet(router.NewAuthRouter, admin.NewBaseRouter, admin.NewEndpointRouter, admin.NewMineRouter, admin.NewSystemRouter, admin.NewAgencyRouter, admin.NewAfterSalesRouter, admin.NewReportRouter, agency.NewMineRouter, agency.NewBaseRouter, agency.NewReimbursementRouter, app.NewMkbRouter, provideRouter)

// 直接提供Router，避免通过IRouterGroup接口
func provideRouter(
	authHandler *auth.Auth,
	captchaHandler base2.CaptchaHandler,
	endpointRouter *admin.EndpointRouter,
	systemRouter *admin.SystemRouter,
	agencyRouter *admin.AgencyRouter,
	afterSalesRouter *admin.AfterSalesRouter,
	baseRouter *admin.BaseRouter,
	mineRouter *admin.MineRouter,
	agencyMineRouter *agency.MineRouter,
	mkbRouter *app.MkbRouter,
	cfg *config.Config,
	authService auth2.ServiceInterface,
	adminUserService system.AdminUserInterface,
	reportRouter *admin.ReportRouter,
	systemAppSvc system.AppSystemSvc,
	baseAgencyRouter *agency.BaseRouter,
	reimbursementRouter *agency.ReimbursementRouter,
) *router.Router {

	authGroup := router.NewAuthRouterGroup(authHandler, captchaHandler)
	adminGroup := router.NewAdminRouterGroup(endpointRouter, systemRouter, agencyRouter, cfg, authService, afterSalesRouter, baseRouter, mineRouter, reportRouter)
	agencyGroup := router.NewAgencyRouterGroup(authService, adminUserService, cfg, agencyMineRouter, baseAgencyRouter, reimbursementRouter)
	appGroup := router.NewAppRouterGroup(authService, cfg, mkbRouter, systemAppSvc)
	thirdPartyGroup := router.NewThirdPartyRoutes(cfg)

	groups := []router.IRouterGroup{
		authGroup,
		adminGroup,
		agencyGroup,
		appGroup,
		thirdPartyGroup,
	}

	return router.NewRouter(groups)
}
