package reimbursement

import (
	api "marketing/internal/api/reimbursement"
	"marketing/internal/consts"
	dao1 "marketing/internal/dao"
	"marketing/internal/dao/admin_user"
	dao "marketing/internal/dao/reimbursement"
	"marketing/internal/model"
	appErr "marketing/internal/pkg/errors"

	"github.com/gin-gonic/gin"
)

type HomePageService interface {
	GetHomePage(c *gin.Context, req *api.HomePageReq) (*api.HomePageResp, error)
	GetClientSummary(c *gin.Context, req *api.ClientSummaryReq) ([]*api.ClientSummaryResp, int64, error)
	GetAllPolicies(c *gin.Context) ([]*model.ReimbursementPolicy, error)
	GetClientOrders(c *gin.Context, req *api.ClientOrdersReq) (*api.ClientSummaryResp, []*api.ClientOrdersResp, error)
	GetClientDetail(c *gin.Context, req *api.OrderReq) (*api.ReimbursementDetail, error)
	ApplyAdvertExpense(c *gin.Context, req *api.AdvertExpenseApplyReq) error
	ChangeAdvertExpense(c *gin.Context, req *api.AdvertExpenseApplyReq) (bool, error)
	ApplyPromotionalProducts(c *gin.Context, req *api.PromotionalProductsApplyReq) error
	ChangePromotionalProducts(c *gin.Context, req *api.PromotionalProductsApplyReq) (bool, error)
	GetPromotionalProducts(c *gin.Context, policyID int) ([]*api.PromotionalProductDetail, error)
	UploadReceipt(c *gin.Context, req *api.UploadReceiptReq) error
	AmountConfirm(c *gin.Context, req *api.AmountConfirmReq) error
	CreateContact(c *gin.Context, req *api.CreateContactReq) error
	GetContacts(c *gin.Context, uid uint) ([]map[string]interface{}, error)
	UpdateContact(c *gin.Context, req *api.UpdateContactReq) (bool, error)
}

type homePageService struct {
	repo          dao.ReimbursementRepository
	userRepo      admin_user.UserDao
	balanceRepo   dao.BalanceRepository
	policyRepo    dao.PolicyRepository
	kingdeeAgency dao1.KingDeeAgencyDao
}

func NewHomePageService(repo dao.ReimbursementRepository, userRepo admin_user.UserDao, balanceRepo dao.BalanceRepository, policyRepo dao.PolicyRepository) HomePageService {
	return &homePageService{
		repo:          repo,
		userRepo:      userRepo,
		balanceRepo:   balanceRepo,
		policyRepo:    policyRepo,
		kingdeeAgency: dao1.NewKingDeeAgencyDao(),
	}
}

// GetHomePage 获取报销首页数据
func (s *homePageService) GetHomePage(c *gin.Context, req *api.HomePageReq) (*api.HomePageResp, error) {
	// 获取当前用户ID
	uid := c.GetUint("uid")

	if uid == 0 {
		return nil, appErr.NewErr("用户未登录")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return nil, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return nil, appErr.NewErr("用户代理不存在")
	}

	// 获取金额信息
	standardBalanceQuota, err := s.balanceRepo.GetBalanceStandard(c, 0, req.CompanyID, "standard_balance_quota")
	if err != nil {
		return nil, err
	}

	// 获取待处理的申请单提醒
	pendingPromotionalProducts, err := s.repo.GetPendingPromotionalProducts(c, req.CompanyID, userAgency.ID)
	if err != nil {
		return nil, err
	}

	pendingAdvertExpense, err := s.repo.GetPendingAdvertExpense(c, req.CompanyID, userAgency.ID)
	if err != nil {
		return nil, err
	}

	pendingReimbursementList, err := s.repo.GetPendingReimbursementList(c, req.CompanyID, userAgency.ID)
	if err != nil {
		return nil, err
	}

	// 合并所有待处理任务
	var pendingTasks []api.PendingTask
	pendingTasks = append(pendingTasks, pendingPromotionalProducts...)
	pendingTasks = append(pendingTasks, pendingReimbursementList...)
	pendingTasks = append(pendingTasks, pendingAdvertExpense...)

	resp := &api.HomePageResp{
		StandardBalanceQuota: standardBalanceQuota.Balance,
		EstimateAmount:       0,
		PendingTask:          pendingTasks,
	}

	return resp, nil
}

// GetClientSummary 获取客户端汇总数据
func (s *homePageService) GetClientSummary(c *gin.Context, req *api.ClientSummaryReq) ([]*api.ClientSummaryResp, int64, error) {
	// 获取当前用户ID
	uid := c.GetUint("uid")

	if uid == 0 {
		return nil, 0, appErr.NewErr("用户未登录")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return nil, 0, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return nil, 0, appErr.NewErr("非总代用户，无权操作")
	}

	req.TopAgency = userAgency.ID

	results, total, err := s.repo.GetClientSummary(c, req)
	if err != nil {
		return nil, 0, appErr.NewErr("获取汇总数据失败: " + err.Error())
	}

	return results, total, nil
}

func (s *homePageService) GetAllPolicies(c *gin.Context) ([]*model.ReimbursementPolicy, error) {
	return s.policyRepo.GetAllPolicy()
}

// GetClientOrders 获取客户端订单数据
func (s *homePageService) GetClientOrders(c *gin.Context, req *api.ClientOrdersReq) (*api.ClientSummaryResp, []*api.ClientOrdersResp, error) {
	// 获取当前用户ID
	uid := c.GetUint("uid")

	if uid == 0 {
		return nil, nil, appErr.NewErr("用户未登录")
	}
	//获取核销政策
	policyInfo, err := s.policyRepo.GetPolicyByID(req.PolicyID)
	if err != nil {
		return nil, nil, err
	}
	if policyInfo == nil {
		return nil, nil, appErr.NewErr("未找到核销政策信息")
	}
	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return nil, nil, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return nil, nil, appErr.NewErr("非总代用户，无权操作")
	}
	req.TopAgency = userAgency.ID
	//订单统计
	summary, err := s.repo.GetPolicyCompanySummary(c, req.PolicyID, req.CompanyID, userAgency.ID)
	if err != nil {
		return nil, nil, appErr.NewErr("获取订单统计信息失败: " + err.Error())
	}
	// 根据不同状态获取不同类型的订单数据
	switch {
	case req.Status >= 1 && req.Status <= 3:
		if policyInfo.PolicyType == "promotional_products" {
			data, err := s.repo.GetPromotionalProductsOrders(c, req)
			return summary, data, err
		} else if policyInfo.PolicyType == "advert_expense" {
			data, err := s.repo.GetAdvertExpenseOrders(c, req)
			return summary, data, err
		}
	case req.Status == 4:
		if policyInfo.PolicyType == "promotional_products" {
			data, err := s.repo.GetPromotionalProductsApplySummaryOrders(c, req)
			return summary, data, err
		} else if policyInfo.PolicyType == "advert_expense" {
			data, err := s.repo.GetAdvertExpenseApplySummaryOrders(c, req)
			return summary, data, err
		}
	case req.Status == 5 || req.Status == 6:
		data, err := s.repo.GetReimbursementOrders(c, req)
		return summary, data, err
	}

	return nil, nil, appErr.NewErr("无效的状态值")
}

func (s *homePageService) GetClientDetail(c *gin.Context, req *api.OrderReq) (*api.ReimbursementDetail, error) {

	// 获取当前用户ID
	uid := c.GetUint("uid")
	if uid == 0 {
		return nil, appErr.NewErr("用户未登录")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return nil, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return nil, appErr.NewErr("非总代用户，无权操作")
	}

	data, err := s.repo.ReimbursementClientDetail(c, req.ID, userAgency.ID)
	if err != nil {
		return nil, err
	}
	//查询summary
	summaries, err := s.repo.GetDetailSummaryInfo(c, req.ID)
	if err != nil {
		return nil, appErr.NewErr("获取汇总信息失败: " + err.Error())
	}
	for i, summary := range summaries {
		if data.PolicyType == "promotional_products" {
			// 获取促销品申请单的详细信息
			product, err := s.repo.GetReimbursementProducts(c, summary.ID)
			if err != nil {
				return nil, appErr.NewErr("获取促销品申请单失败: " + err.Error())
			}
			if product == nil {
				return nil, appErr.NewErr("未找到促销品申请单")
			}
			summaries[i].Products = product
		}
		//获取apply_info
		applyInfo, err := s.repo.GetReimbursementApplyInfo(c, summary.ID, data.PolicyType)
		if err != nil {
			return nil, appErr.NewErr("获取申请信息失败: " + err.Error())
		}
		summaries[i].ApplicationInfo = applyInfo
	}
	data.ReimbursementSummary = summaries

	return data, nil
}

func (s *homePageService) ApplyAdvertExpense(c *gin.Context, req *api.AdvertExpenseApplyReq) error {
	uid := c.GetUint("uid")
	if uid == 0 {
		return appErr.NewErr("用户未登录")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return appErr.NewErr("权限不足")
	}
	agency := userAgency.ID

	// 获取并验证政策信息
	policyInfo, err := s.policyRepo.GetPolicyByID(req.PolicyID)
	if err != nil {
		return err
	}
	if policyInfo == nil {
		return appErr.NewErr("此政策已过期, 无法修改")
	}
	if policyInfo.Archive == 1 {
		return appErr.NewErr("此政策已归档, 无法修改")
	}
	// 获取公司
	companyInfo, err := s.kingdeeAgency.GetKingDeeAgencyByID(c, req.CompanyID)
	if err != nil {
		return appErr.NewErr("获取公司信息失败: " + err.Error())
	}
	if companyInfo == nil {
		return appErr.NewErr("未找到此公司信息, 无法申请核销")
	}
	if companyInfo.TopAgency != agency {
		return appErr.NewErr("此公司非此代理商名下, 无法申请核销")
	}
	req.Uid = c.GetUint("uid")
	req.Agency = agency
	req.Code = companyInfo.Code
	req.ReimbursementType = policyInfo.ReimbursementType
	// 调用DAO层进行申请
	err = s.repo.ApplyAdvertExpense(c, req)
	if err != nil {
		return appErr.NewErr("申请广告费用失败: " + err.Error())
	}

	return nil
}

// ChangeAdvertExpense 广告费用申请单修改
func (s *homePageService) ChangeAdvertExpense(c *gin.Context, req *api.AdvertExpenseApplyReq) (bool, error) {
	// 获取当前用户ID
	order, policyInfo, agency, err := s.validateAdvertOrder(c, req.ID)
	if err != nil {
		return false, err
	}

	// 检查状态是否允许修改
	if order.Status != 0 {
		return false, appErr.NewErr("状态错误，无法修改")
	}

	// 验证金额
	if req.Amount <= 0 {
		return false, appErr.NewErr("金额要求大于0")
	}
	req.Uid = c.GetUint("uid")
	req.Agency = agency
	req.Code = order.Code
	req.ReimbursementType = policyInfo.ReimbursementType

	// 调用DAO层进行修改
	return s.repo.ChangeAdvertExpense(c, req)
}

func (s *homePageService) ApplyPromotionalProducts(c *gin.Context, req *api.PromotionalProductsApplyReq) error {
	// 获取当前用户ID
	// 获取当前用户ID
	uid := c.GetUint("uid")

	if uid == 0 {
		return appErr.NewErr("用户未登录")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return appErr.NewErr("权限不足")
	}
	// 获取政策
	policyInfo, err := s.policyRepo.GetPolicyByID(req.PolicyID)
	if policyInfo == nil {
		return appErr.NewErr("此政策已过期, 无法修改")
	}
	if policyInfo.Archive == 1 {
		return appErr.NewErr("此政策已归档, 无法修改")
	}

	agency := userAgency.ID
	req.Uid = uid
	// 获取公司
	companyInfo, err := s.kingdeeAgency.GetKingDeeAgencyByID(c, req.CompanyID)
	if err != nil {
		return appErr.NewErr("获取公司信息失败: " + err.Error())
	}
	if companyInfo == nil {
		return appErr.NewErr("未找到此公司信息, 无法申请核销")
	}

	if companyInfo.TopAgency != agency {
		return appErr.NewErr("此公司非此代理商名下, 无法申请核销")
	}
	req.Uid = c.GetUint("uid")
	req.Agency = agency
	req.CompanyCode = companyInfo.Code
	req.ReimbursementType = policyInfo.ReimbursementType
	// 验证促销品额度
	quota, err := s.repo.GetPolicyQuantityQuota(c, req.PolicyID, 0)
	if quota == 0 {
		return appErr.NewErr("促销品额度不足, 无法申请")
	}
	if quota > 0 && req.QuantityTotal > quota {
		return appErr.NewErr("促销品申请额度超过了当前可用额度")
	}
	// 调用DAO层进行申请
	err = s.repo.ApplyPromotionalProducts(c, req)
	if err != nil {
		return appErr.NewErr("促销品核销申请失败: " + err.Error())
	}

	return nil
}

// ChangePromotionalProducts 促销品费用申请单修改
func (s *homePageService) ChangePromotionalProducts(c *gin.Context, req *api.PromotionalProductsApplyReq) (bool, error) {
	// 获取当前用户ID
	order, policyInfo, agency, err := s.validateProductsOrder(c, req.ID)
	if err != nil {
		return false, err
	}

	req.Uid = c.GetUint("uid")
	req.Agency = agency
	req.CompanyCode = order.Code
	req.ReimbursementType = policyInfo.ReimbursementType
	// 验证促销品额度
	quota, err := s.repo.GetPolicyQuantityQuota(c, req.PolicyID, req.ID)
	if quota == 0 {
		return false, appErr.NewErr("促销品额度不足, 无法申请")
	}
	if quota > 0 && req.QuantityTotal > quota {
		return false, appErr.NewErr("促销品申请额度超过了当前可用额度")
	}
	// 调用DAO层进行修改
	return s.repo.ChangePromotionalProducts(c, req)
}

func (s *homePageService) GetPromotionalProducts(c *gin.Context, policyID int) ([]*api.PromotionalProductDetail, error) {
	data, err := s.policyRepo.GetPromotionalProductsByPolicyID(policyID)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (s *homePageService) UploadReceipt(c *gin.Context, req *api.UploadReceiptReq) error {
	// 获取当前用户ID
	order, _, _, err := s.validateProductsOrder(c, req.ID)
	if err != nil {
		return err
	}
	if order.Status != 1 {
		return appErr.NewErr("状态错误，无法上传收货单")
	}
	return s.repo.UploadReceipt(c, req)
}

func (s *homePageService) AmountConfirm(c *gin.Context, req *api.AmountConfirmReq) error {
	// 获取当前用户ID
	order, _, _, err := s.validateProductsOrder(c, req.ID)
	if err != nil {
		return err
	}
	if order.Status != 0 {
		return appErr.NewErr("状态错误，无法确认核销金额")
	}
	if order.CompletionStatus == 1 {
		return appErr.NewErr("已确认过核销金额，无需再次确认")
	}
	//获取汇总订单信息
	summaries, err := s.repo.GetSummaryOrderList(c, req.ID)
	if err != nil {
		return appErr.NewErr("获取汇总信息失败: " + err.Error())
	}
	req.SummaryOrder = summaries

	// 调用DAO层进行金额确认
	return s.repo.AmountConfirm(c, req)
}

// validateAdvertOrder 验证订单和政策
func (s *homePageService) validateAdvertOrder(c *gin.Context, orderID int) (*model.ReimbursementAdvertExpenseList, *model.ReimbursementPolicy, uint, error) {
	// 获取当前用户ID
	uid := c.GetUint("uid")
	if uid == 0 {
		return nil, nil, 0, appErr.NewErr("用户未登录")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return nil, nil, 0, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return nil, nil, 0, appErr.NewErr("权限不足")
	}

	// 获取申请单详情
	agencyMap := map[string]int{
		"top_agency":    int(userAgency.ID),
		"second_agency": 0,
	}
	order, err := s.repo.GetAdvertExpenseOrder(c, orderID, agencyMap)
	if err != nil {
		return nil, nil, 0, err
	}
	if order == nil {
		return nil, nil, 0, appErr.NewErr("未找到记录，无法修改")
	}

	if order.TopAgency != userAgency.ID {
		return nil, nil, 0, appErr.NewErr("无权操作，非此总代订单")
	}

	// 获取并验证政策信息
	policyInfo, err := s.repo.GetPolicyByOrder(c, orderID, 2)
	if err != nil {
		return nil, nil, 0, err
	}
	if policyInfo == nil {
		return nil, nil, 0, appErr.NewErr("此政策已过期, 无法修改")
	}
	if policyInfo.Archive == 1 {
		return nil, nil, 0, appErr.NewErr("此政策已归档, 无法修改")
	}

	return order, policyInfo, userAgency.ID, nil
}

// validateOrderAndPolicy 验证订单和政策
func (s *homePageService) validateProductsOrder(c *gin.Context, orderID int) (*model.ReimbursementPromotionalProductsList, *model.ReimbursementPolicy, uint, error) {
	// 获取当前用户ID
	uid := c.GetUint("uid")
	if uid == 0 {
		return nil, nil, 0, appErr.NewErr("用户未登录")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return nil, nil, 0, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return nil, nil, 0, appErr.NewErr("权限不足")
	}

	// 获取订单信息
	order, err := s.repo.GetPromotionalProductsOrder(c, orderID, nil)
	if err != nil {
		return nil, nil, 0, err
	}
	if order == nil {
		return nil, nil, 0, appErr.NewErr("未找到记录，无法修改")
	}

	if order.TopAgency != userAgency.ID {
		return nil, nil, 0, appErr.NewErr("无权操作，非此总代订单")
	}

	// 获取并验证政策信息
	policyInfo, err := s.repo.GetPolicyByOrder(c, orderID, 1)
	if err != nil {
		return nil, nil, 0, err
	}
	if policyInfo == nil {
		return nil, nil, 0, appErr.NewErr("此政策已过期, 无法修改")
	}
	if policyInfo.Archive == 1 {
		return nil, nil, 0, appErr.NewErr("此政策已归档, 无法修改")
	}

	return order, policyInfo, userAgency.ID, nil
}

// CreateContact 创建联系人
func (s *homePageService) CreateContact(c *gin.Context, req *api.CreateContactReq) error {
	// 获取当前用户ID
	uid := c.GetUint("uid")
	if uid == 0 {
		return appErr.NewErr("用户未登录")
	}

	// 获取用户信息和角色
	user, err := s.userRepo.GetUserWithRoles(c, uid)
	if err != nil {
		return appErr.NewErr("权限不足")
	}
	if user == nil {
		return appErr.NewErr("权限不足")
	}
	if *user.Status != 1 {
		return appErr.NewErr("此用户已被禁用")
	}

	// 检查用户是否有总代角色
	hasTopAgencyRole := false
	for _, role := range user.Roles {
		if role.Slug == consts.RoleTopAgency {
			hasTopAgencyRole = true
			break
		}
	}
	if !hasTopAgencyRole {
		return appErr.NewErr("非总代用户，无权操作")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return appErr.NewErr("权限不足")
	}

	// 调用DAO层创建联系人
	err = s.repo.CreateContact(c, uid, userAgency.ID, req)
	if err != nil {
		return appErr.NewErr("添加联系人失败，系统出错")
	}

	return nil
}

// GetContacts 获取联系人列表
func (s *homePageService) GetContacts(c *gin.Context, uid uint) ([]map[string]interface{}, error) {
	// 获取用户信息和角色
	user, err := s.userRepo.GetUserWithRoles(c, uid)
	if err != nil {
		return nil, appErr.NewErr("权限不足")
	}
	if user == nil {
		return nil, appErr.NewErr("权限不足")
	}
	if *user.Status != 1 {
		return nil, appErr.NewErr("此用户已被禁用")
	}

	// 检查用户是否有总代角色
	hasTopAgencyRole := false
	for _, role := range user.Roles {
		if role.Slug == consts.RoleTopAgency {
			hasTopAgencyRole = true
			break
		}
	}
	if !hasTopAgencyRole {
		return nil, appErr.NewErr("非总代用户，无权操作")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return nil, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return nil, appErr.NewErr("权限不足")
	}

	// 调用DAO层获取联系人列表
	contacts, err := s.repo.GetContacts(c, userAgency.ID)
	if err != nil {
		return nil, appErr.NewErr("获取联系人列表失败")
	}

	return contacts, nil
}

// UpdateContact 修改联系人
func (s *homePageService) UpdateContact(c *gin.Context, req *api.UpdateContactReq) (bool, error) {
	// 获取当前用户ID
	uid := c.GetUint("uid")
	if uid == 0 {
		return false, appErr.NewErr("用户未登录")
	}

	// 获取用户信息和角色
	user, err := s.userRepo.GetUserWithRoles(c, uid)
	if err != nil {
		return false, appErr.NewErr("权限不足")
	}
	if user == nil {
		return false, appErr.NewErr("权限不足")
	}
	if *user.Status != 1 {
		return false, appErr.NewErr("此用户已被禁用")
	}

	// 检查用户是否有总代角色
	hasTopAgencyRole := false
	for _, role := range user.Roles {
		if role.Slug == consts.RoleTopAgency {
			hasTopAgencyRole = true
			break
		}
	}
	if !hasTopAgencyRole {
		return false, appErr.NewErr("非总代用户，无权操作")
	}

	// 获取用户代理信息
	userAgency, err := s.userRepo.UserAgency(c, uid)
	if err != nil {
		return false, appErr.NewErr("权限不足")
	}
	if userAgency == nil {
		return false, appErr.NewErr("权限不足")
	}

	// 调用DAO层修改联系人
	success, err := s.repo.UpdateContact(c, uid, userAgency.ID, req)
	if err != nil {
		return false, appErr.NewErr("修改联系人失败，系统出错")
	}

	return success, nil
}
